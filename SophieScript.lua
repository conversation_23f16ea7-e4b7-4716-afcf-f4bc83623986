--initialization values
addonName, scripty = ...
local backgroundColor1 = {r=95 / 255, g=140 / 255, b=158 / 255, a=1}
local backgroundColor2 = {r=20 / 255, g=80 / 255, b=102 / 255, a=1}
local highlightColor1 = {r=174 / 255, g=209 / 255, b=103 / 255, a=1}
local highlightColor2 = {r=158 / 255, g=80 / 255, b=81 / 255, a=1}
local whiteColor = {r=255 / 255, g=255 / 255, b=255 / 255, a=1}
local grayColor = {r=150 / 255, g= 150 / 255, b=150 / 255, a=1}
local greenColor = {r=0.01, g=0.55, b=0.1, a=0.75}
local redColor = {r=0.55, g=0.01, b=0.1, a=0.75}
local mainWindowWidth = 360
local mainWindowHeight = 215
local routineTrayHeight = 64
local routineTrayButtonWidth = 64
local routineTrayButtonHeight = 32
local routineTrayButtonSpacing = 4
local routineTrayMargin = 10
local SophieScriptFrame = nil
local RoutineFrame = nil
local currentRoutine = nil
local bindingsLoaded = false
local activatedTrigger = {}
local activatedTriggerIndex = nil
local frameHolder = {}
local mainScriptWindow = {}
local routineTrayFrame = {}
scripty.Data = scripty.Data or {}

-- Routine tray data
scripty.Data.RoutineTray = {}
scripty.Data.RoutineTray.Buttons = {}

--config
scripty.Data.attackDisabled = false
scripty.Data.forceAttackMode = false
scripty.Data.mayUseSavedVariables = false


SophieScriptDB = SophieScriptDB or {}
local TextFrames = {}
local ExtraFrames = {}
local DebugFrames = {}
local ExtraDebugFrames = {}

function SophieScript() 
	scripty.Data.currentRoutine = scripty.Routines.Prot		
	scripty.Data.routineTriggersEnabled = scripty.Data.routineTriggersEnabled or {}
	for _, trigger in pairs(scripty.Data.currentRoutine.TriggerList) do
		table.insert(scripty.Data.routineTriggersEnabled, true)

		if trigger.Name == nil then
			print("Trigger missing name field. Check yo spelling")
		elseif trigger.Binding == nil then
			print("Trigger '" .. trigger.Name .. "' missing 'Binding' field. Check yo spelling!")
		elseif trigger.Conditions == nil then
			print("Trigger '" .. trigger.Name .. "' missing 'Conditions' field. Check yo spelling!")
		end
	end
	SophieScriptFrame = CreateFrame("Frame",nil,UIParent)
	SophieScriptFrame:SetFrameStrata("BACKGROUND")
	SophieScriptFrame:SetWidth(2) -- Set these to whatever height/width is needed 
	SophieScriptFrame:SetHeight(2) -- for your Texture
	SophieScriptFrame:SetPoint("TOPLEFT", 0, 0)
	SophieScriptFrame.texture = SophieScriptFrame:CreateTexture()
	SophieScriptFrame.texture:SetAllPoints(SophieScriptFrame)
	SophieScriptFrame.texture:SetColorTexture(1,1,1,1)
	SophieScriptFrame:RegisterEvent("UNIT_SPELLCAST_START")
	SophieScriptFrame:RegisterEvent("UNIT_SPELLCAST_SUCCEEDED")
	SophieScriptFrame:RegisterEvent("UNIT_SPELLCAST_SENT")
	SophieScriptFrame:RegisterEvent("UNIT_SPELLCAST_CHANNEL_START")
	SophieScriptFrame:RegisterEvent("UI_ERROR_MESSAGE")
	SophieScriptFrame:RegisterEvent("PLAYER_TARGET_CHANGED")
	SophieScriptFrame:RegisterEvent("LOOT_OPENED")
	SophieScriptFrame:RegisterEvent("SPELL_UPDATE_CHARGES")
	SophieScriptFrame:RegisterEvent("ADDON_LOADED")
	SophieScriptFrame:RegisterEvent("PLAYER_REGEN_ENABLED")
	SophieScriptFrame:RegisterEvent("PLAYER_REGEN_DISABLED")
	SophieScriptFrame:RegisterEvent("COMBAT_LOG_EVENT_UNFILTERED")
	SophieScriptFrame:RegisterEvent("UNIT_SPELLCAST_FAILED")
	SophieScriptFrame:RegisterEvent("PLAYER_TALENT_UPDATE")
	SophieScriptFrame:RegisterEvent("LOSS_OF_CONTROL_UPDATE")
	SophieScriptFrame:SetScript("OnEvent", EventHandler);

	-- Setup Main window with a title and dragging support
	mainScriptWindow = CreateFrame("Frame", nil, UIParent);
	mainScriptWindow:SetWidth(mainWindowWidth)  
	mainScriptWindow:SetHeight(mainWindowHeight + routineTrayHeight)
	mainScriptWindow:SetMovable(true)
	mainScriptWindow:EnableMouse(true)
	mainScriptWindow:RegisterForDrag("LeftButton")
	mainScriptWindow:SetScript("OnDragStart", mainScriptWindow.StartMoving)
	mainScriptWindow:SetScript("OnDragStop", mainScriptWindow.StopMovingOrSizing)
	mainScriptWindow:SetPoint("RIGHT", -5, 0)
	mainScriptWindow.texture = mainScriptWindow:CreateTexture()
	mainScriptWindow.texture:SetAllPoints(mainScriptWindow)
	mainScriptWindow.texture:SetColorTexture(0,0,0,0.75)
	mainScriptWindow.text = mainScriptWindow:CreateFontString(nil,"ARTWORK", "GameFontNormal")
	mainScriptWindow.text:SetPoint("TOP", 0, -10)
	mainScriptWindow.text:SetJustifyH("LEFT")
	-- mainScriptWindow.text:SetJustifyV("CENTER")
	mainScriptWindow.text:SetTextColor(whiteColor.r, whiteColor.g, whiteColor.b, whiteColor.a)

	-- Setup child window that will actually hold the rest of the UI elements
	frameHolder = CreateFrame("Frame", nil, mainScriptWindow);
	frameHolder:SetWidth(mainWindowWidth)  
	frameHolder:SetHeight(mainWindowHeight) 
	frameHolder:SetPoint("TOPLEFT", 0, -30)

	frameHolder.scrollframe = frameHolder.scrollframe or CreateFrame("ScrollFrame", "SophieScriptScrollFrame", frameHolder, "UIPanelScrollFrameTemplate");
	frameHolder.scrollchild = frameHolder.scrollchild or CreateFrame("Frame");
	local scrollbarName = frameHolder.scrollframe:GetName()
	frameHolder.scrollbar = _G[scrollbarName.."ScrollBar"];
	frameHolder.scrollupbutton = _G[scrollbarName.."ScrollBarScrollUpButton"];
	frameHolder.scrolldownbutton = _G[scrollbarName.."ScrollBarScrollDownButton"];
	frameHolder.scrollupbutton:ClearAllPoints();
	frameHolder.scrollupbutton:SetPoint("TOPRIGHT", frameHolder.scrollframe, "TOPRIGHT", -2, -2);
	frameHolder.scrolldownbutton:ClearAllPoints();
	frameHolder.scrolldownbutton:SetPoint("BOTTOMRIGHT", frameHolder.scrollframe, "BOTTOMRIGHT", -2, 2);
	frameHolder.scrollbar:ClearAllPoints();
	frameHolder.scrollbar:SetPoint("TOP", frameHolder.scrollupbutton, "BOTTOM", 0, -2);
	frameHolder.scrollbar:SetPoint("BOTTOM", frameHolder.scrolldownbutton, "TOP", 0, 2);
	frameHolder.scrollframe:SetScrollChild(frameHolder.scrollchild);
	frameHolder.scrollframe:SetAllPoints(frameHolder);
	frameHolder.scrollchild:SetSize(frameHolder.scrollframe:GetWidth(), ( frameHolder.scrollframe:GetHeight() * 2 ));
	frameHolder.listFrame = CreateFrame("Frame", nil, frameHolder.scrollchild);
	frameHolder.listFrame:SetAllPoints(frameHolder.scrollchild)

	mainScriptWindow:Hide()

	routineTrayFrame = CreateFrame("Frame", nil, mainScriptWindow);
	routineTrayFrame:SetWidth(mainWindowWidth)  
	routineTrayFrame:SetHeight(routineTrayHeight) 
	routineTrayFrame:SetPoint("BOTTOMLEFT", 0, 0)
	-- SetupRoutineTray()
	
	SetRoutine(scripty.Data.currentRoutine)
end

local lastRun = GetTime()

function SophieScriptOnFrame()
	if scripty.Data.pauseTime and GetTime() - scripty.Data.pauseTime < 2 then
		return
	end
	if scripty.Data.talentsUpdated then
		AutoTriggerRoutine()
		scripty.Data.talentsUpdated = false
	end
	if not bindingsLoaded and not UnitAffectingCombat("player") then
		scripty.Bindings.LoadBindings(scripty.Data.currentRoutine.TriggerList)
		bindingsLoaded = true
	end
	if not bindingsLoaded then
		return
	end
	local timeSinceLastRun = GetTime() - lastRun
	if timeSinceLastRun > 0.1 then
		lastRun = GetTime()

		-- Check if we're in a pet battle and send page down color instead
		if C_PetBattles and C_PetBattles.IsInBattle() then
			local pageDownColor = GetColorForKey('PAGEDOWN')
			if pageDownColor > 0 then
				SophieScriptFrame.texture:SetColorTexture(pageDownColor / 255,0,0,1)
			end
			return
		end

		if scripty.Data.spellQueued then
			if scripty.Data.spellQueuedTime and GetTime() - scripty.Data.spellQueuedTime > 3 then
				scripty.Data.spellQueued = nil
			else
				print("spell " .. scripty.Data.spellQueued .. " queued with binding:" .. scripty.Data.spellQueuedBinding)
				ActivateBinding(scripty.Data.spellQueuedBinding)
				return
			end
		end
		activatedTrigger, activatedTriggerIndex = RunRoutine(scripty.Data.currentRoutine)
		if activatedTrigger then
			local focus = scripty.Data.UnitFromUnit(activatedTrigger.Focus)
			scripty.Data.lastTrigger = activatedTrigger
			scripty.Data.lastTriggerTime = GetTime()
			if focus then
				scripty.Data.lastTriggerWasHostile = false
			else
				scripty.Data.lastTriggerWasHostile = true
			end
			if focus then
				if not scripty.Conditions.UnitIsUnit("focus", focus)() then
					local focusBinding = "MACRO " .. focus
					ActivateBinding(focusBinding)
				else
					ActivateBinding(activatedTrigger.Binding)
				end
			else
				-- print(activatedTrigger.Binding)
				ActivateBinding(activatedTrigger.Binding)
			end
			UpdateTriggerTexts(activatedTriggerIndex)
		else
			UpdateTriggerTexts(0)
			ResetBinding()
		end
		-- UpdateDebugTexts()
	end
	
end

function ToggleMainWindow()
	if mainScriptWindow:IsVisible() then
		mainScriptWindow:Hide()
	else
		mainScriptWindow:Show()
	end
end

function SophieBind()
	scripty.Bindings.LoadBindings(scripty.Data.currentRoutine.TriggerList)
end

function AutoTriggerRoutine()
	local routine = nil
	if UnitClass("player") == "Druid" then
		if GetSpecialization() == 1 then
			routine = scripty.Routines.Boomkin
		end
		if GetSpecialization() == 2 then
			routine = scripty.Routines.Feral
		end
		if GetSpecialization() == 3 then
			routine = scripty.Routines.LaserBear
		end
		if GetSpecialization() == 4 then
			routine = scripty.Routines.Tree
		end
	end
	if UnitClass("player") == "Paladin" then
		if GetSpecialization() == 1 then
			routine = scripty.Routines.HolyPala
		end
		if GetSpecialization() == 2 then
			routine = scripty.Routines.ProtPala
		end
		if GetSpecialization() == 3 then
			routine = scripty.Routines.Ret
		end
	end
	if UnitClass("player") == "Priest" then
		if GetSpecialization() == 1 then
			routine = scripty.Routines.Disc
		end
		if GetSpecialization() == 2 then
			routine = scripty.Routines.Holy
		end
		if GetSpecialization() == 3 then
			routine = scripty.Routines.Shadow
		end
	end
	if UnitClass("player") == "Hunter" then
		if GetSpecialization() == 1 then
			routine = scripty.Routines.BM
		end
		if GetSpecialization() == 2 then
			routine = scripty.Routines.Marks
		end
	end
	if UnitClass("player") == "Death Knight" then
		if GetSpecialization() == 1 then
			routine = scripty.Routines.SophieDK
		end
	end
	if UnitClass("player") == "Warlock" then
		if GetSpecialization() == 1 then
			routine = scripty.Routines.Affliction
		end
		if GetSpecialization() == 2 then
			routine = scripty.Routines.Demo
		end
	end
	if UnitClass("player") == "Monk" then
		if GetSpecialization() == 1 then
			routine = scripty.Routines.Brew
		end
		if GetSpecialization() == 2 then
			routine = scripty.Routines.Mist
		end
		if GetSpecialization() == 3 then
			routine = scripty.Routines.WW
		end
	end
	if UnitClass("player") == "Shaman" then
		if GetSpecialization() == 1 then
			routine = scripty.Routines.Elemental
		end
		if GetSpecialization() == 2 then
			routine = scripty.Routines.Enhance
		end
		if GetSpecialization() == 3 then
			routine = scripty.Routines.RestoShammy
		end
	end
	if UnitClass("player") == "Rogue" then
		if GetSpecialization() == 1 then
			routine = scripty.Routines.Assass
		end
		if GetSpecialization() == 2 then
			routine = scripty.Routines.Outlaw
		end
		if GetSpecialization() == 3 then
			routine = scripty.Routines.Subtlety
		end
	end
	if UnitClass("player") == "Mage" then
		if GetSpecialization() == 1 then
			routine = scripty.Routines.Arcane
		end
		if GetSpecialization() == 2 then
			routine = scripty.Routines.Fire
		end
		if GetSpecialization() == 3 then
		end
	end
	if routine then
		SetRoutine(routine)
	end
end

function RunRoutine(routine)
	routine.DataHarvester()
	if PlayerIsCastingMount() then
		return nil
	end
	if IsMounted() or ChatEdit_GetActiveWindow() then
		return nil
	end
	local int i = 0
	for _, trigger in pairs(routine.TriggerList) do
		i = i + 1
		if scripty.Data.routineTriggersEnabled[i] then
			local shouldActivateTrigger = true
			local focus = scripty.Data.UnitFromUnit(trigger.Focus)
			if focus == "no one" then
				shouldActivateTrigger = false
			end
			for _, condition in pairs(trigger.Conditions) do
				if not condition() then
					shouldActivateTrigger = false
					break
				end
			end
			if shouldActivateTrigger then
				return trigger, i
			end
		end
	end
	return nil
end

function PlayerIsCastingMount()
	local name, text, texture, startTimeMS, endTimeMS, isTradeSkill, castID, notInterruptible, spellID = UnitCastingInfo("player")
	if spellID and C_MountJournal and C_MountJournal.GetMountFromSpell(spellID) then
		return true
	end
	_, _, _, _, _, _, _, spellID = UnitChannelInfo("player")
	if spellID and C_MountJournal and C_MountJournal.GetMountFromSpell(spellID) then
		return true
	end
	return false
end

function ClearCurrentRoutine()
	-- while #DebugFrames > 0 do
	-- 	local debugFrameTable = table.remove(DebugFrames);
	-- 	while #debugFrameTable > 0 do
	-- 		local debugFrame = table.remove(debugFrameTable)
	-- 		debugFrame:Hide()
	-- 		table.insert(ExtraDebugFrames, debugFrame)
	-- 	end
	-- end

	while #TextFrames > 0 do
		local textFrame = table.remove(TextFrames);
		textFrame:Hide()
		table.insert(ExtraFrames, textFrame)
	end
end

function SetRoutine(routine)
	ClearCurrentRoutine()

	scripty.Data.routineTriggersEnabled = {}
	scripty.Data.currentRoutine = routine
	for _, trigger in pairs(scripty.Data.currentRoutine.TriggerList) do
		table.insert(scripty.Data.routineTriggersEnabled, true) 
		if trigger.Name == nil then
			print("Trigger missing name field. Check yo spelling")
		elseif trigger.Binding == nil then
			print("Trigger '" .. trigger.Name .. "' missing 'Binding' field. Check yo spelling!")
		elseif trigger.Conditions == nil then
			print("Trigger '" .. trigger.Name .. "' missing 'Conditions' field. Check yo spelling!")
		end
	end

	--Build out UI list of triggers / debug flags
	local lastFrame = nil
	local k = 0
	for _, trigger in pairs(scripty.Data.currentRoutine.TriggerList) do
		k = k + 1
		TextFrames = TextFrames or {}
		local textFrame = nil
		if #ExtraFrames > 0 then
			textFrame = table.remove(ExtraFrames)
		end
		if not textFrame then
			textFrame = CreateFrame("Frame", nil, frameHolder.listFrame)
		end
		if lastFrame then
			textFrame:SetPoint("TOP", lastFrame, "BOTTOM")
		else
			textFrame:SetPoint("TOP", frameHolder.listFrame, "TOP", 0, -10)
		end
		textFrame:SetPoint("LEFT", frameHolder.listFrame, 10, 0)
		textFrame:SetPoint("RIGHT", frameHolder.listFrame, -20, 0)
		textFrame:SetHeight(20)
		textFrame.texture = textFrame.texture or textFrame:CreateTexture(nil, "BACKGROUND")
		textFrame.texture:SetAllPoints()
		textFrame.text = textFrame.text or textFrame:CreateFontString(nil,"ARTWORK", "GameFontNormal")
		textFrame.text:SetPoint("TOP", textFrame)
		textFrame.text:SetPoint("BOTTOM", textFrame)
		textFrame.text:SetPoint("RIGHT", textFrame)
		textFrame.text:SetPoint("LEFT", textFrame, "LEFT", 25, 0)
		textFrame.text:SetJustifyH("LEFT")
		-- textFrame.text:SetJustifyV("CENTER")
		
		textFrame.text:SetTextColor(whiteColor.r, whiteColor.g, whiteColor.b, whiteColor.a)
		textFrame.text:SetText(trigger.Name)
		textFrame:Show();

		textFrame.checkbox =  textFrame.checkbox or CreateFrame('CheckButton', nil, frameHolder.listFrame, "ChatConfigCheckButtonTemplate")
		textFrame.checkbox:SetPoint("LEFT", textFrame)
		textFrame.checkbox:SetPoint("TOP", textFrame, "TOP", 0, 2)
		textFrame.checkbox:SetSize(24,24)
		textFrame.checkbox:SetChecked(true)
		local c = k
		textFrame.checkbox:SetScript('OnClick', function()
			scripty.Data.routineTriggersEnabled[c] = not scripty.Data.routineTriggersEnabled[c]
			UpdateTriggerTexts(0)
		end)
		
		lastFrame = textFrame
		table.insert(TextFrames, textFrame)
		SetTextFrameBackgroundColorTexture(k)

	-- 	local lastDebugFrame = nil
	-- 	local int i = 0
	-- 	for _, condition in pairs(trigger.Conditions) do
	-- 		i = i + 1
	-- 		DebugFrames = DebugFrames or {}
	-- 		local debugFrame = nil
	-- 		if #ExtraFrames > 0 then
	-- 			debugFrame = table.remove(ExtraFrames)
	-- 			debugFrame:SetParent(textFrame)
	-- 		end
	-- 		if not debugFrame then
	-- 			debugFrame = CreateFrame("Frame", nil, textFrame)
	-- 		end

	-- 		debugFrame:SetPoint("RIGHT", frameHolder.listFrame, "RIGHT", -15 * (#trigger.Conditions - i) - 30, 0)
	-- 		debugFrame:SetPoint("LEFT", frameHolder.listFrame, "LEFT", 10, 0)
	-- 		debugFrame:SetPoint("TOP", textFrame)
	-- 		debugFrame:SetHeight(20)
	-- 		debugFrame.text = textFrame:CreateFontString(nil,"ARTWORK", "GameFontNormal")
	-- 		debugFrame.text:SetAllPoints(debugFrame)
	-- 		debugFrame.text:SetJustifyH("RIGHT")
	-- 		debugFrame.text:SetJustifyV("CENTER")
	-- 		debugFrame.text:SetTextColor(highlightColor2.r, highlightColor2.g, highlightColor2.b, highlightColor2.a)
	-- 		debugFrame.text:SetText(i)
	-- 		debugFrame:Show()

	-- 		lastDebugFrame = debugFrame
	-- 		DebugFrames[k] = DebugFrames[k] or {}
	-- 		table.insert(DebugFrames[k], debugFrame)
	-- 	end
	end

	-- "Title area" text that lists number of loaded triggers
	mainScriptWindow.text:SetText(scripty.Data.currentRoutine.Name .. ": " .. k .. " triggers loaded")

	bindingsLoaded = false
end

function SetTextFrameBackgroundColorTexture(i)
	if not scripty.Data.routineTriggersEnabled[i] then
		TextFrames[i].texture:SetColorTexture(grayColor.r, grayColor.g, grayColor.b, grayColor.a)
		return
	end
	if i % 2 == 0 then
		TextFrames[i].texture:SetColorTexture(backgroundColor1.r, backgroundColor1.g, backgroundColor1.b, backgroundColor1.a)
	else
		TextFrames[i].texture:SetColorTexture(backgroundColor2.r, backgroundColor2.g, backgroundColor2.b, backgroundColor2.a)
	end
end

function UpdateTriggerTexts(currentSelectionIndex)
	for i = 1, #TextFrames do 
		if i == currentSelectionIndex then
			TextFrames[i].texture:SetColorTexture(highlightColor1.r, highlightColor1.g, highlightColor1.b, highlightColor1.a)
		else
			SetTextFrameBackgroundColorTexture(i)
		end
	end
end

function UpdateDebugTexts()
	for index, debugFrameTable in pairs(DebugFrames) do
		for i = 1, #debugFrameTable do
			local triggerList = scripty.Data.currentRoutine.TriggerList
			if not triggerList[index] then
				print("Something wrong with routine item number " .. index)
			end
			if not triggerList[index].Conditions[i] then
				print("Missing Condition for trigger " .. triggerList[index].Name)
			end
			if scripty.Data.currentRoutine.TriggerList[index].Conditions[i]() then
				debugFrameTable[i].text:SetTextColor(highlightColor1.r, highlightColor1.g, highlightColor1.b, highlightColor1.a)
			else
				debugFrameTable[i].text:SetTextColor(highlightColor2.r, highlightColor2.g, highlightColor2.b, highlightColor2.a)
			end
		end
	end
	
end

function ResetBinding()
	SophieScriptFrame.texture:SetColorTexture(0,0,0,1)
	lastBinding = nil
	lastBindingTimeChange = GetTime()
end

local lastBindingTimeChange = GetTime()
local lastBinding = nil

function ActivateBinding(binding)
	scripty.Data.AttemptedBindingActivationTable = scripty.Data.AttemptedBindingActivationTable or {}
	scripty.Data.AttemptedBindingActivationTable[binding] = GetTime()
	if lastBinding ~= binding then
		lastBinding = binding
		lastBindingTimeChange = GetTime()
	end
	if GetTime() - lastBindingTimeChange > 4 and binding then
	--	print("possibly stuck on " .. binding)
	end
	local keyForBinding = scripty.Bindings.KeyForBinding(binding)
	-- print(binding)
	-- print(keyForBinding)
	local color = GetColorForKey(keyForBinding)
	-- print(color)
	if color == 0 then
		--print("No color for bind:" .. binding)
		return
	end
	SophieScriptFrame.texture:SetColorTexture(color / 255,0,0,1)
end

function SetupRoutineTray()
	print("Setting up routine tray for " .. scripty.Data.currentRoutine.Name)

	local x = routineTrayMargin
	local y = -5
	for i, buttonInfo in ipairs(scripty.Data.currentRoutine.ButtonList) do
		
		-- Error checking to maintain sanity
		if buttonInfo.Property == nil then
			print("ButtonInfo is missing 'Property' field. Index: " .. tostring(i) .. ", routine: " .. scripty.Data.currentRoutine.Name)
		elseif buttonInfo.Text == nil then
			print("ButtonInfo is missing 'Text' field. Index: " .. tostring(i) .. ", routine: " .. scripty.Data.currentRoutine.Name)
		elseif scripty.Data[buttonInfo.Property] == nil then
			print("Property Name:  " .. buttonInfo.Property .. ", is nil in 'scripty.Data. Index: " .. tostring(i) .. ", routine: " .. scripty.Data.currentRoutine.Name)
		end

		-- Fetch the value of the property using lua magic
		local propertyValue = scripty.Data[buttonInfo.Property]

		-- Create the button
		local b = CreateFrame("Button", nil, routineTrayFrame, UIPanelButtonTemplate)
		b:SetWidth(routineTrayButtonWidth)
		b:SetHeight(routineTrayButtonHeight)
		b.texture = b:CreateTexture()
		b.texture:SetAllPoints(b)
		
		-- Set the background color based on true/false of property
		local buttonBackgroundColor = propertyValue and greenColor or redColor
		b.texture:SetColorTexture(buttonBackgroundColor.r, buttonBackgroundColor.g, buttonBackgroundColor.b, buttonBackgroundColor.a)
		b:SetNormalFontObject("ChatFontNormal")
		b:SetPoint("TOPLEFT", x, y)
		
		-- Simple text format based on propertyValue and buttonInfo.Text field
		b:SetText(buttonInfo.Text .. "\n" .. (propertyValue and "enabled" or "disabled"))
		
		-- Button Click handler
		b:SetScript("OnClick", 
			function(self, btn)
				scripty.Data[buttonInfo.Property] = not scripty.Data[buttonInfo.Property]
				UpdateRoutineTrayButton(buttonInfo, b)
			end
		)

		-- Layout
		x = x + routineTrayButtonSpacing + routineTrayButtonWidth
		if i == 5 then
			y = -5 - routineTrayButtonHeight - routineTrayButtonSpacing
			x = routineTrayMargin
		end
	end
end

-- Used to update a button given the button reference and buttonInfo object from a Routine's ButtonList
-- Don't call this function outside of the click handler in SetupRoutineTray
function UpdateRoutineTrayButton(buttonInfo, button)
	local buttonBackgroundColor = scripty.Data[buttonInfo.Property] and greenColor or redColor
	button.texture:SetColorTexture(buttonBackgroundColor.r, buttonBackgroundColor.g, buttonBackgroundColor.b, buttonBackgroundColor.a)
	button:SetText(buttonInfo.Text .. "\n" .. (scripty.Data[buttonInfo.Property] and "enabled" or "disabled"))
end

function FreezeUnitInfo()
	local enemyData = scripty.Data.EnemyDataBlock("target")
	--Iterate over the data to create a UI element for each data element
	-- if not enemyData then
	-- 	print("No enemy data found")
	-- 	return
	-- end
	local backdropInfo =
	{
		bgFile = "Interface\\Tooltips\\UI-Tooltip-Background",
		edgeFile = "Interface\\Tooltips\\UI-Tooltip-Border",
		tile = true,
		tileEdge = true,
		tileSize = 8,
		edgeSize = 8,
		insets = { left = 1, right = 1, top = 1, bottom = 1 },
	}
	local frame = CreateFrame("Frame", nil, nil, "BackdropTemplate")
	frame:SetWidth(400)
	frame:SetHeight(400)
	frame:SetPoint("CENTER", 0, 0)
	frame:SetBackdrop(backdropInfo)
	frame:SetMovable(true)
	frame:EnableMouse(true)
	frame:SetScript("OnMouseDown", function(self, button)
		if button == "LeftButton" then
			self:StartMoving()
		end
	end)
	frame:SetScript("OnMouseUp", function(self, button)
		if button == "LeftButton" then
			self:StopMovingOrSizing()
		end
	end)
	frame:Show()

	local closeButton = CreateFrame("Button", nil, frame, "UIPanelCloseButton")
	closeButton:SetPoint("TOPRIGHT", frame, "TOPRIGHT", -8, -8)
	closeButton:SetScript("OnClick", 
		function() 
			frame:Hide() 
		end)

	if scripty.Data.mayUseSavedVariables then
		SophieScriptDB.Enemies = SophieScriptDB.Enemies or {}
		local dropdownMenu = CreateFrame("Frame", "EnemyDropdown", frame, "UIDropDownMenuTemplate")
		dropdownMenu:SetPoint("TOPLEFT", frame, "TOPLEFT", 10, -10)
		UIDropDownMenu_SetWidth(dropdownMenu, 200)
		UIDropDownMenu_SetText(dropdownMenu, "Select Zone")

		local dropdownMenu2 = CreateFrame("Frame", "SubEnemyDropdown", frame, "UIDropDownMenuTemplate")
		dropdownMenu2:SetPoint("TOPLEFT", dropdownMenu, "BOTTOMLEFT", 0, -10)
		UIDropDownMenu_SetWidth(dropdownMenu2, 200)
		UIDropDownMenu_SetText(dropdownMenu2, "Select Enemy")

		local selectedZone = GetZoneText()
		local function OnClick(self)
			UIDropDownMenu_SetSelectedID(dropdownMenu, self:GetID())
			selectedZone = self.value
		end

		local function initialize(self, level)
			local info = UIDropDownMenu_CreateInfo()
			for key, value in pairs(SophieScriptDB.Enemies) do
				info = UIDropDownMenu_CreateInfo()
				info.text = key
				info.value = key
				info.func = OnClick
				UIDropDownMenu_AddButton(info, level)
			end
		end
		UIDropDownMenu_Initialize(dropdownMenu, initialize)

		local function initialize2(self, level)
			local info = UIDropDownMenu_CreateInfo()
			for key, value in pairs(SophieScriptDB.Enemies[selectedZone]) do
				info = UIDropDownMenu_CreateInfo()
				info.text = key
				info.value = key
				info.func = function(self)
					UIDropDownMenu_SetSelectedID(dropdownMenu2, self:GetID())
					enemyData = scripty.Data.EnemyDataBlockFromNameAndZone(key, selectedZone)
					AddTableKVToFrame(frame, enemyData, 80, 10)
				end
				UIDropDownMenu_AddButton(info, level)
			end
		end
		UIDropDownMenu_Initialize(dropdownMenu2, initialize2)
	end

	AddTableKVToFrame(frame, enemyData, 80, 10)
end

local scrollFrame = nil

function AddTableKVToFrame(frame, table, yOffset, xOffset)
	if not table then
		return yOffset
	end
	if scrollFrame then
		local scrollChild = scrollFrame:GetScrollChild()
		if scrollChild then
			scrollChild:Hide()
			scrollChild:SetParent(nil)
		end
	else 
		scrollFrame = CreateFrame("ScrollFrame", nil, frame, "UIPanelScrollFrameTemplate")
		scrollFrame:SetAllPoints(frame)
	end

	local scrollChild = CreateFrame("Frame", nil, scrollFrame)
	scrollChild:SetSize(200, 2000)
	scrollFrame:SetScrollChild(scrollChild)

	local scrollChildListFrame = CreateFrame("Frame", nil, scrollChild)
	scrollChildListFrame:SetAllPoints(scrollChild)

	AddFreezeFrameScrollComponents(scrollChildListFrame, table, yOffset, xOffset)
end

function AddFreezeFrameScrollComponents(frame, table, yOffset, xOffset)
	for key, value in pairs(table) do
		if type(value) == "table" then
			local label = frame:CreateFontString(nil, "OVERLAY", "GameFontNormal")
			label:SetPoint("TOPLEFT", xOffset, -yOffset)
			label:SetText(key)
			yOffset = yOffset + 30
			xOffset = xOffset + 20
			yOffset = AddFreezeFrameScrollComponents(frame, value, yOffset, xOffset)
			xOffset = xOffset - 20
		else 
			local label = frame:CreateFontString(nil, "OVERLAY", "GameFontNormal")
			label:SetPoint("TOPLEFT", xOffset, -yOffset)
			label:SetText(key)
			
			local editBox = CreateFrame("EditBox", nil, frame, "InputBoxTemplate")
			editBox:SetPoint("TOPLEFT", label, "TOPRIGHT", 10, 0)
			editBox:SetWidth(100)
			editBox:SetHeight(20)
			editBox:SetText(tostring(value))
			editBox:SetScript("OnTextChanged", function(self)
				if type(value) == "number" then
					table[key] = tonumber(self:GetText())
				elseif type(value) == "boolean" then
					table[key] = self:GetText() == "true"
				else
					table[key] = self:GetText()
				end
			end)
			yOffset = yOffset + 30
		end
	end
	return yOffset
end



function GetColorForKey(key)
	color = 0
	if key == 'F1' then
		color = 1
	elseif key == 'F2' then
		color = 2
	elseif key == 'F3' then
		color = 3
	elseif key == 'F4' then
		color = 4
	elseif key == 'F5' then
		color = 5
	elseif key == 'F6' then
		color = 6
	elseif key == 'F7' then
		color = 7
	elseif key == 'F8' then
		color = 8
	elseif key == 'F9' then
		color = 9
	elseif key == 'F10' then
		color = 10
	elseif key == 'F11' then
		color = 11
	elseif key == 'F12' then
		color = 12
	elseif key == '6' then
		color = 13
	elseif key == '7' then
		color = 14
	elseif key == '8' then
		color = 15
	elseif key == '9' then
		color = 16
	elseif key == '0' then
		color = 17
	elseif key == '-' then
		color = 18
	elseif key == '=' then
		color = 19
	elseif key == 'NUMPAD1' then
		color = 20
	elseif key == 'NUMPAD2' then
		color = 21
	elseif key == 'NUMPAD3' then
		color = 22
	elseif key == 'NUMPAD4' then
		color = 23
	elseif key == 'NUMPAD5' then
		color = 24
	elseif key == 'NUMPAD6' then
		color = 25
	elseif key == 'NUMPAD7' then
		color = 26
	elseif key == 'NUMPAD8' then
		color = 27
	elseif key == 'NUMPAD9' then
		color = 28
	elseif key == 'NUMPAD0' then
		color = 29
	elseif key == 'NUMPADDECIMAL' then
		color = 30
	elseif key == 'NUMPADMINUS' then
		color = 31
	elseif key == 'NUMPADPLUS' then
		color = 32
	elseif key == 'NUMPADMULTIPLY' then
		color = 33
	elseif key == 'NUMPADDIVIDE' then
		color = 34
	elseif key == ',' then
		color  = 35
	elseif key == '.' then
		color = 36
	elseif key == 'LEFT' then
		color = 37
	elseif key == 'UP' then
		color = 38
	elseif key == 'RIGHT' then
		color = 39
	elseif key == 'DOWN' then
		color = 40
	elseif key == ';' then
		color = 41
	elseif key == '\'' then
		color = 42
	elseif key == '[' then
		color = 43
	elseif key == ']' then
		color = 44
	elseif key == 'SHIFT-END' then
		color = 45
	elseif key == 'SHIFT-F1' then
		color = 46
	elseif key == 'SHIFT-F2' then
		color = 47
	elseif key == 'SHIFT-F3' then
		color = 48
	elseif key == 'SHIFT-F4' then
		color = 49
	elseif key == 'SHIFT-F5' then
		color = 50
	elseif key == 'SHIFT-F6' then
		color = 51
	elseif key == 'SHIFT-F7' then
		color = 52
	elseif key == 'SHIFT-F8' then
		color = 53
	elseif key == 'SHIFT-F9' then
		color = 54
	elseif key == 'SHIFT-F10' then
		color = 55
	elseif key == 'SHIFT-F11' then
		color = 56
	elseif key == 'SHIFT-F12' then
		color = 57
	elseif key == 'SHIFT-6' then
		color = 58
	elseif key == 'SHIFT-7' then
		color = 59
	elseif key == 'SHIFT-8' then
		color = 60
	elseif key == 'SHIFT-9' then
		color = 61
	elseif key == 'SHIFT-0' then
		color = 62
	elseif key == 'SHIFT--' then
		color = 63
	elseif key == 'SHIFT-=' then
		color = 64
	elseif key == 'SHIFT-,' then
		color = 65
	elseif key == 'SHIFT-.' then
		color = 66
	elseif key == 'SHIFT-;' then
		color = 67
	elseif key == 'SHIFT-\'' then
		color = 68
	elseif key == 'SHIFT-[' then
		color = 69
	elseif key == 'SHIFT-]' then
		color = 70
	elseif key == 'HOME' then
		color = 71
	elseif key == 'END' then
		color = 72
	elseif key == 'PAGEUP' then
		color = 73
	elseif key == 'PAGEDOWN' then
		color = 74
	elseif key == 'SHIFT-LEFT' then
		color = 75
	elseif key == 'SHIFT-UP' then
		color = 76
	elseif key == 'SHIFT-RIGHT' then
		color = 77
	elseif key == 'SHIFT-DOWN' then
		color = 78
	elseif key == 'SHIFT-HOME' then
		color = 79
	elseif key == 'SHIFT-PAGEUP' then
		color = 80
	elseif key == 'SHIFT-PAGEDOWN' then
		color = 81
	elseif key == 'SHIFT-u' then
		color = 82
	elseif key == 'SHIFT-i' then
		color = 83
	end
	return color
end

function SophieForceAttackTarget()
	if scripty.Data.attackDisabled then
		return
	end
	if UnitExists("target") then
		scripty.Data.forceAttackTargetGUID = UnitGUID("target")
	end
end

function SophieLockTarget()
	scripty.Data.lockAttackTarget = not scripty.Data.lockAttackTarget
end

function EventHandler(self, event, ...)
	scripty.Data.currentRoutine.EventHandler(event, ...)
end

function PrintAurasSophie()
    local _, class = UnitClass("player")
    print("Current Auras for " .. UnitName("player") .. " (" .. class .. "):")
    
    -- Iterate through each aura on the player
    for i = 1, 40 do
        local name, _, _, _, duration, expirationTime, _, _, _, spellId = UnitAura("player", i, "HARMFUL")
        if not name then
            break
        end
        
        -- Format duration
        local remainingTime = expirationTime - GetTime()
        local durationText = ""
        if duration > 0 then
            durationText = string.format("%.1f", remainingTime) .. "s"
        end
        
        -- Print aura information
        print(name .. " (ID: " .. spellId .. ") - Duration: " .. durationText)
    end
end